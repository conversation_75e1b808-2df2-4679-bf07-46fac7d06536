# Install docling - only need to run this once
!pip install docling

# Import necessary libraries
from docling.document_converter import DocumentConverter
import os
from pathlib import Path

# Define the path to the example PDF
pdf_path = "assets/example.pdf"

# Check if the file exists
if os.path.exists(pdf_path):
    print(f"✅ PDF file found: {pdf_path}")
    print(f"📄 File size: {os.path.getsize(pdf_path) / 1024:.2f} KB")
else:
    print(f"❌ PDF file not found: {pdf_path}")
    print("Current directory contents:")
    print(os.listdir("."))

# Initialize the DocumentConverter
converter = DocumentConverter()

print("📚 DocumentConverter initialized successfully!")
print("🔧 Ready to extract content from PDF documents")

# Extract content from the PDF
print("🔄 Processing PDF document...")

# Convert the PDF document
result = converter.convert(pdf_path)
document = result.document

print("✅ PDF processed successfully!")
print(f"📊 Document contains {len(document.pages)} page(s)")

# Display basic document information
print("\n📋 Document Information:")
# Check available attributes
print(f"• Document type: {type(document).__name__}")
print(f"• Pages: {len(document.pages)}")

# Get counts of different elements
text_count = len(document.texts) if hasattr(document, 'texts') else 0
table_count = len(document.tables) if hasattr(document, 'tables') else 0  
picture_count = len(document.pictures) if hasattr(document, 'pictures') else 0

print(f"• Total elements: {text_count + table_count + picture_count}")
print(f"• Text elements: {text_count}")
print(f"• Tables: {table_count}")
print(f"• Pictures: {picture_count}")

# Extract text content as markdown
markdown_content = document.export_to_markdown()

print("📝 Text Content (Markdown format):")
print("=" * 50)
print(markdown_content)
print("=" * 50)

# Extract and display individual text elements
print("📑 Individual Text Elements:")
print("=" * 50)

for i, text_element in enumerate(document.texts[:5]):  # Show first 5 text elements
    print(f"\n🔹 Text Element {i+1}:")
    print(f"   Content: {text_element.text[:100]}{'...' if len(text_element.text) > 100 else ''}")
    print(f"   Label: {text_element.label}")

if len(document.texts) > 5:
    print(f"\n... and {len(document.texts) - 5} more text elements")

print("=" * 50)

# Extract tables if any exist
if document.tables:
    print("📊 Tables Found:")
    print("=" * 50)
    
    for i, table in enumerate(document.tables):
        print(f"\n🔹 Table {i+1}:")
        
        # Check available attributes
        table_attrs = [attr for attr in dir(table) if not attr.startswith('_')]
        print(f"   Available attributes: {table_attrs[:5]}...")  # Show first 5 attributes
        
        # Try to get table text content
        if hasattr(table, 'text') and table.text:
            print(f"   Content preview: {table.text[:100]}{'...' if len(table.text) > 100 else ''}")
        
        # Try to export as markdown if available
        if hasattr(table, 'export_to_markdown'):
            try:
                table_md = table.export_to_markdown()
                print(f"   Markdown preview: {table_md[:200]}{'...' if len(table_md) > 200 else ''}")
            except Exception as e:
                print(f"   Could not export to markdown: {e}")
                
else:
    print("📊 No tables found in the document")

print("=" * 50)

# Save extracted content to files
output_dir = "extracted_content"
os.makedirs(output_dir, exist_ok=True)

# Save as markdown
markdown_file = os.path.join(output_dir, "extracted_content.md")
with open(markdown_file, 'w', encoding='utf-8') as f:
    f.write(markdown_content)

# Save as JSON for structured access
json_content = document.export_to_dict()
import json
json_file = os.path.join(output_dir, "extracted_content.json")
with open(json_file, 'w', encoding='utf-8') as f:
    json.dump(json_content, f, indent=2, ensure_ascii=False)

print(f"💾 Content saved to:")
print(f"   📄 Markdown: {markdown_file}")
print(f"   📄 JSON: {json_file}")
print(f"   📁 Output directory: {output_dir}")

# Display directory contents
print(f"\n📁 Files in {output_dir}:")
for file in os.listdir(output_dir):
    file_path = os.path.join(output_dir, file)
    size_kb = os.path.getsize(file_path) / 1024
    print(f"   • {file} ({size_kb:.2f} KB)")

# Batch convert all PDFs in assets/
from pathlib import Path
import os
import shutil
import json
from datetime import datetime

assets_dir = Path("assets")
output_root = Path("extracted_content")
output_root.mkdir(parents=True, exist_ok=True)

if not assets_dir.exists():
    raise FileNotFoundError(f"Assets directory not found: {assets_dir.resolve()}")

pdf_files = sorted([p for p in assets_dir.rglob("*.pdf") if p.is_file()])
print(f"Found {len(pdf_files)} PDF(s) under {assets_dir}")

if not pdf_files:
    print("No PDFs to process. Add files to the assets/ folder.")

results = []
errors = []

# Reuse existing converter if present, else create one
try:
    converter
except NameError:
    from docling.document_converter import DocumentConverter
    converter = DocumentConverter()

for idx, pdf_path in enumerate(pdf_files, start=1):
    rel = pdf_path.relative_to(assets_dir)
    stem = pdf_path.stem
    # Keep subfolder structure under extracted_content
    target_dir = output_root / rel.parent / stem
    target_dir.mkdir(parents=True, exist_ok=True)

    print("\n==============================")
    print(f"[{idx}/{len(pdf_files)}] Processing: {rel}")
    print(f"Output dir: {target_dir}")

    try:
        # Convert
        result = converter.convert(str(pdf_path))
        document = result.document

        # Export markdown
        md_text = document.export_to_markdown()
        md_file = target_dir / "extracted_content.md"
        with md_file.open("w", encoding="utf-8") as f:
            f.write(md_text)

        # Export JSON
        json_data = document.export_to_dict()
        json_file = target_dir / "extracted_content.json"
        with json_file.open("w", encoding="utf-8") as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)

        # Copy original PDF into the folder
        pdf_copy = target_dir / f"{stem}.pdf"
        # If source and destination are same path, skip copy
        if pdf_path.resolve() != pdf_copy.resolve():
            shutil.copy2(pdf_path, pdf_copy)

        page_count = len(getattr(document, "pages", []))
        results.append({
            "pdf": str(rel),
            "output_dir": str(target_dir.relative_to(output_root.parent)),
            "pages": page_count,
            "markdown": str(md_file),
            "json": str(json_file)
        })
        print(f"✅ Done: {rel} (pages: {page_count})")
    except Exception as e:
        errors.append({"pdf": str(rel), "error": str(e)})
        print(f"❌ Error processing {rel}: {e}")

# Summary
print("\n==============================")
print("Batch Summary")
print("==============================")
print(f"Processed: {len(results)} / {len(pdf_files)} PDF(s)")
if results:
    for r in results:
        print(f"• {r['pdf']} -> {r['output_dir']}")
if errors:
    print("\nErrors:")
    for err in errors:
        print(f"• {err['pdf']}: {err['error']}")