#!/usr/bin/env python3
"""
Integration tests for the separated modules
Tests the interaction between core_logic and cli modules
"""

import asyncio
import tempfile
import os
import json
from unittest.mock import Mock, patch, AsyncMock
from rdflib import Graph

# Import both modules
import core_logic
from cli import EnhancedTTLQACLI


class IntegrationTestRunner:
    """Integration test runner"""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []

    def test(self, name: str, test_func):
        """Run a single test"""
        try:
            print(f"🧪 Testing {name}...")
            test_func()
            print(f"✅ {name} passed")
            self.passed += 1
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            self.failed += 1
            self.errors.append(f"{name}: {e}")

    async def async_test(self, name: str, test_func):
        """Run a single async test"""
        try:
            print(f"🧪 Testing {name}...")
            await test_func()
            print(f"✅ {name} passed")
            self.passed += 1
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            self.failed += 1
            self.errors.append(f"{name}: {e}")

    def create_temp_files(self):
        """Create temporary test files"""
        # Create TTL file
        ttl_content = """
        @prefix ex: <http://example.org/> .
        @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
        
        ex:building1 rdf:type ex:Building .
        ex:building1 ex:hasName "Test Building" .
        ex:building1 ex:hasFloors 5 .
        
        ex:building2 rdf:type ex:Building .
        ex:building2 ex:hasName "Another Building" .
        ex:building2 ex:hasFloors 3 .
        """
        
        ttl_file = tempfile.NamedTemporaryFile(mode='w', suffix='.ttl', delete=False)
        ttl_file.write(ttl_content)
        ttl_file.close()
        
        # Create ontology file
        ontology_content = """
        @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
        @prefix ex: <http://example.org/> .
        
        ex:hasName rdfs:domain ex:Building .
        ex:hasName rdfs:range rdfs:Literal .
        ex:hasFloors rdfs:domain ex:Building .
        ex:hasFloors rdfs:range rdfs:Literal .
        """
        
        ontology_file = tempfile.NamedTemporaryFile(mode='w', suffix='.ttl', delete=False)
        ontology_file.write(ontology_content)
        ontology_file.close()
        
        # Create guidelines file
        guidelines_data = {
            "Domain": {
                "Classifications": {
                    "$values": [
                        {
                            "Name": "Building",
                            "ClassificationProperties": {
                                "$values": [
                                    {
                                        "Name": "hasName",
                                        "Description": "Building name",
                                        "IsRequired": True,
                                        "PropertyAssignment": {
                                            "Property": {"StorageType": 4}
                                        }
                                    },
                                    {
                                        "Name": "hasFloors",
                                        "Description": "Number of floors",
                                        "IsRequired": False,
                                        "PropertyAssignment": {
                                            "Property": {"StorageType": 1}
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        }
        
        guidelines_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(guidelines_data, guidelines_file)
        guidelines_file.close()
        
        return ttl_file.name, ontology_file.name, guidelines_file.name

    def cleanup_temp_files(self, *files):
        """Clean up temporary files"""
        for file_path in files:
            try:
                os.unlink(file_path)
            except:
                pass

    def test_core_logic_imports(self):
        """Test that core_logic module imports correctly"""
        # Test that all main components are available
        assert hasattr(core_logic, 'TimingEntry')
        assert hasattr(core_logic, 'TimingTracker')
        assert hasattr(core_logic, 'OntologyContext')
        assert hasattr(core_logic, 'GuidelinesContext')
        assert hasattr(core_logic, 'EnhancedTTLContext')
        assert hasattr(core_logic, 'analyze_ontology')
        assert hasattr(core_logic, 'analyze_guidelines')
        assert hasattr(core_logic, 'validate_sparql_query')
        assert hasattr(core_logic, 'enhanced_ttl_qa_agent')

    def test_cli_imports(self):
        """Test that CLI module imports correctly"""
        # Test that CLI class is available
        cli = EnhancedTTLQACLI()
        assert cli is not None
        assert hasattr(cli, 'console')
        assert hasattr(cli, 'current_context')
        assert hasattr(cli, 'load_enhanced_ttl_file')
        assert hasattr(cli, 'ask_enhanced_question')

    def test_enhanced_ttl_context_creation(self):
        """Test creating EnhancedTTLContext with real data"""
        ttl_file, ontology_file, guidelines_file = self.create_temp_files()
        
        try:
            # Test individual analysis functions
            ontology_context = core_logic.analyze_ontology(ontology_file)
            assert isinstance(ontology_context, core_logic.OntologyContext)
            
            guidelines_context = core_logic.analyze_guidelines(guidelines_file)
            assert isinstance(guidelines_context, core_logic.GuidelinesContext)
            
            basic_analysis = core_logic.analyze_ttl_file_basic(ttl_file)
            assert isinstance(basic_analysis, str)
            assert "Total triples:" in basic_analysis
            
            # Test creating full context
            graph = Graph()
            graph.parse(ttl_file, format='turtle')
            
            context = core_logic.EnhancedTTLContext(
                graph=graph,
                file_path=ttl_file,
                basic_analysis=basic_analysis,
                ontology_context=ontology_context,
                guidelines_context=guidelines_context
            )
            
            assert context.graph is not None
            assert len(context.graph) > 0
            assert context.file_path == ttl_file
            assert isinstance(context.ontology_context, core_logic.OntologyContext)
            assert isinstance(context.guidelines_context, core_logic.GuidelinesContext)
            
        finally:
            self.cleanup_temp_files(ttl_file, ontology_file, guidelines_file)

    async def test_agent_tools_integration(self):
        """Test that agent tools work together"""
        ttl_file, ontology_file, guidelines_file = self.create_temp_files()
        
        try:
            # Create enhanced context
            graph = Graph()
            graph.parse(ttl_file, format='turtle')
            
            ontology_context = core_logic.analyze_ontology(ontology_file)
            guidelines_context = core_logic.analyze_guidelines(guidelines_file)
            basic_analysis = core_logic.analyze_ttl_file_basic(ttl_file)
            
            context = core_logic.EnhancedTTLContext(
                graph=graph,
                file_path=ttl_file,
                basic_analysis=basic_analysis,
                ontology_context=ontology_context,
                guidelines_context=guidelines_context
            )
            
            # Mock context for agent tools
            mock_context = Mock()
            mock_context.deps = context
            
            # Test context requirements analysis
            with patch('core_logic.context_analysis_agent.run') as mock_agent:
                mock_result = Mock()
                mock_result.output = core_logic.ContextRequirements(
                    needs_ontology=True,
                    needs_guidelines=False,
                    needs_analysis_only=False,
                    reasoning="Test reasoning",
                    question_type="counting",
                    confidence=0.9,
                    key_concepts=["building"],
                    expected_complexity="low"
                )
                mock_agent.return_value = mock_result
                
                requirements = await core_logic.analyze_context_requirements(
                    mock_context, "How many buildings are there?"
                )
                assert isinstance(requirements, core_logic.ContextRequirements)
                assert requirements.needs_ontology is True
            
            # Test ontology context extraction
            ontology_info = await core_logic.extract_ontology_context(
                mock_context, "What properties do buildings have?"
            )
            assert isinstance(ontology_info, str)
            
            # Test guidelines context extraction
            guidelines_info = await core_logic.extract_guidelines_context(
                mock_context, "What are the validation rules for hasName?"
            )
            assert isinstance(guidelines_info, str)
            
            # Test strategy development
            strategy = await core_logic.develop_contextual_strategy(
                mock_context, "How many buildings are there?",
                ontology_context=ontology_info,
                guidelines_context=guidelines_info
            )
            assert isinstance(strategy, core_logic.ContextualStrategy)
            
            # Test SPARQL query execution
            query_result = await core_logic.execute_sparql_query(
                mock_context, "SELECT (COUNT(*) as ?count) WHERE { ?s a <http://example.org/Building> }"
            )
            assert isinstance(query_result, str)
            result_data = json.loads(query_result)
            assert result_data['success'] is True
            
            # Test results analysis
            analysis = await core_logic.analyze_contextual_results(
                mock_context, query_result, "How many buildings are there?"
            )
            assert isinstance(analysis, core_logic.ContextualInsight)
            
        finally:
            self.cleanup_temp_files(ttl_file, ontology_file, guidelines_file)

    def test_cli_context_loading(self):
        """Test CLI context loading functionality"""
        ttl_file, ontology_file, guidelines_file = self.create_temp_files()
        
        try:
            cli = EnhancedTTLQACLI()
            
            # Mock the file input prompts
            with patch('rich.prompt.Prompt.ask', side_effect=[ttl_file, ontology_file, guidelines_file]):
                success = cli.load_enhanced_ttl_file()
                assert success is True
                assert cli.current_context is not None
                assert isinstance(cli.current_context, core_logic.EnhancedTTLContext)
                assert len(cli.current_context.graph) > 0
                
        finally:
            self.cleanup_temp_files(ttl_file, ontology_file, guidelines_file)

    async def test_end_to_end_question_flow(self):
        """Test complete question answering flow"""
        ttl_file, ontology_file, guidelines_file = self.create_temp_files()
        
        try:
            cli = EnhancedTTLQACLI()
            
            # Load context
            with patch('rich.prompt.Prompt.ask', side_effect=[ttl_file, ontology_file, guidelines_file]):
                success = cli.load_enhanced_ttl_file()
                assert success is True
            
            # Mock the agent to avoid actual API calls
            with patch('core_logic.enhanced_ttl_qa_agent.run') as mock_agent:
                mock_result = Mock()
                mock_result.output = "Test answer: There are 2 buildings in the data."
                mock_result.usage.return_value = Mock(requests=1, total_tokens=100)
                mock_agent.return_value = mock_result
                
                # Test asking a question
                await cli.ask_enhanced_question("How many buildings are there?")
                
                # Verify agent was called
                mock_agent.assert_called_once()
                
        finally:
            self.cleanup_temp_files(ttl_file, ontology_file, guidelines_file)

    def test_error_handling(self):
        """Test error handling in various scenarios"""
        cli = EnhancedTTLQACLI()
        
        # Test loading non-existent files
        with patch('rich.prompt.Prompt.ask', side_effect=['/nonexistent.ttl', '/nonexistent.ttl', '/nonexistent.json']):
            success = cli.load_enhanced_ttl_file()
            assert success is False
        
        # Test asking question without context
        assert cli.current_context is None

    async def run_all_tests(self):
        """Run all integration tests"""
        print("🚀 Starting integration test suite...")
        print("=" * 80)
        
        # Test imports and basic functionality
        self.test("Core Logic Imports", self.test_core_logic_imports)
        self.test("CLI Imports", self.test_cli_imports)
        self.test("Enhanced TTL Context Creation", self.test_enhanced_ttl_context_creation)
        self.test("CLI Context Loading", self.test_cli_context_loading)
        self.test("Error Handling", self.test_error_handling)
        
        # Test async integration
        await self.async_test("Agent Tools Integration", self.test_agent_tools_integration)
        await self.async_test("End-to-End Question Flow", self.test_end_to_end_question_flow)
        
        # Print summary
        print("=" * 80)
        print(f"📊 Integration Test Summary:")
        print(f"✅ Passed: {self.passed}")
        print(f"❌ Failed: {self.failed}")
        print(f"📈 Success Rate: {self.passed / (self.passed + self.failed) * 100:.1f}%")
        
        if self.errors:
            print("\n❌ Errors:")
            for error in self.errors:
                print(f"  - {error}")
        
        if self.failed == 0:
            print("\n🎉 All integration tests passed! The separated modules work correctly together.")
        else:
            print(f"\n⚠️  {self.failed} integration tests failed. Please review the errors above.")
        
        return self.failed == 0

async def main():
    """Main integration test runner"""
    runner = IntegrationTestRunner()
    success = await runner.run_all_tests()
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
