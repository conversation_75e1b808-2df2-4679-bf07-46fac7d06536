#!/usr/bin/env python3
"""
Test script to verify the 'list' object has no attribute 'get' fix
"""

import asyncio
from unittest.mock import Mock
from core_logic import analyze_contextual_results

async def test_list_fix():
    """Test that the list input fix works"""
    print("🧪 Testing 'list' object has no attribute 'get' fix...")
    
    mock_context = Mock()
    mock_context.deps = Mock()
    
    try:
        # Test the problematic case - list input that was causing the error
        result = await analyze_contextual_results(
            mock_context,
            [{'name': 'test', 'count': 5}],  # This was causing the error
            'How many items?'
        )
        print('✅ List input test passed:', result.insight)
        
        # Test dict input
        result2 = await analyze_contextual_results(
            mock_context,
            {'success': True, 'data': [{'count': 10}], 'row_count': 1},
            'How many items?'
        )
        print('✅ Dict input test passed:', result2.insight)
        
        # Test string input (JSON)
        import json
        result3 = await analyze_contextual_results(
            mock_context,
            json.dumps({'success': True, 'data': [{'count': 15}], 'row_count': 1}),
            'How many items?'
        )
        print('✅ JSON string input test passed:', result3.insight)
        
        # Test invalid input
        result4 = await analyze_contextual_results(
            mock_context,
            123,  # Invalid type
            'How many items?'
        )
        print('✅ Invalid input test passed:', result4.insight)
        
        print('🎉 All tests passed! The fix is working correctly.')
        return True
        
    except Exception as e:
        print(f'❌ Test failed with error: {e}')
        return False

if __name__ == "__main__":
    success = asyncio.run(test_list_fix())
    if success:
        print("✅ Fix verified successfully!")
    else:
        print("❌ Fix verification failed!")
