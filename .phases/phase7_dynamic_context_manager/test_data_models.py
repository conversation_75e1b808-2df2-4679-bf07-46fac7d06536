"""
Comprehensive unit tests for data models
Tests all Pydantic models and dataclasses with validation and edge cases
"""

import pytest
from typing import Dict, List
from unittest.mock import Mock
from rdflib import Graph

# Import the data models
from core_logic import (
    OntologyContext, GuidelinesContext, EnhancedTTLContext,
    ContextualInsight, EnhancedSPARQLQuery, ContextualStrategy, ContextRequirements
)


class TestOntologyContext:
    """Test cases for OntologyContext dataclass"""

    def test_ontology_context_creation(self):
        """Test basic OntologyContext creation"""
        context = OntologyContext(
            class_relationships={"ClassA": ["ClassB", "ClassC"]},
            property_domains={"prop1": ["ClassA"]},
            property_ranges={"prop1": ["ClassB"]},
            class_properties={"ClassA": ["prop1", "prop2"]},
            property_constraints={"prop1": {"domains": ["ClassA"], "ranges": ["ClassB"]}}
        )
        
        assert context.class_relationships == {"ClassA": ["ClassB", "ClassC"]}
        assert context.property_domains == {"prop1": ["ClassA"]}
        assert context.property_ranges == {"prop1": ["ClassB"]}
        assert context.class_properties == {"ClassA": ["prop1", "prop2"]}
        assert context.property_constraints == {"prop1": {"domains": ["ClassA"], "ranges": ["ClassB"]}}

    def test_ontology_context_empty(self):
        """Test OntologyContext with empty dictionaries"""
        context = OntologyContext(
            class_relationships={},
            property_domains={},
            property_ranges={},
            class_properties={},
            property_constraints={}
        )
        
        assert context.class_relationships == {}
        assert context.property_domains == {}
        assert context.property_ranges == {}
        assert context.class_properties == {}
        assert context.property_constraints == {}

    def test_ontology_context_none_values(self):
        """Test OntologyContext handling None values in lists"""
        context = OntologyContext(
            class_relationships={"ClassA": [None, "ClassB"]},
            property_domains={"prop1": [None]},
            property_ranges={"prop1": []},
            class_properties={"ClassA": ["prop1", None]},
            property_constraints={"prop1": None}
        )
        
        assert None in context.class_relationships["ClassA"]
        assert None in context.property_domains["prop1"]
        assert context.property_ranges["prop1"] == []
        assert None in context.class_properties["ClassA"]
        assert context.property_constraints["prop1"] is None


class TestGuidelinesContext:
    """Test cases for GuidelinesContext dataclass"""

    def test_guidelines_context_creation(self):
        """Test basic GuidelinesContext creation"""
        context = GuidelinesContext(
            property_definitions={"prop1": {"description": "Test property", "class": "TestClass"}},
            validation_rules={"prop1": {"required": True, "min_value": 0}},
            required_properties={"TestClass": ["prop1", "prop2"]},
            property_types={"prop1": "integer", "prop2": "string"},
            property_units={"prop1": "meters (m)"}
        )
        
        assert context.property_definitions["prop1"]["description"] == "Test property"
        assert context.validation_rules["prop1"]["required"] is True
        assert context.required_properties["TestClass"] == ["prop1", "prop2"]
        assert context.property_types["prop1"] == "integer"
        assert context.property_units["prop1"] == "meters (m)"

    def test_guidelines_context_empty(self):
        """Test GuidelinesContext with empty dictionaries"""
        context = GuidelinesContext(
            property_definitions={},
            validation_rules={},
            required_properties={},
            property_types={},
            property_units={}
        )
        
        assert context.property_definitions == {}
        assert context.validation_rules == {}
        assert context.required_properties == {}
        assert context.property_types == {}
        assert context.property_units == {}

    def test_guidelines_context_complex_validation_rules(self):
        """Test GuidelinesContext with complex validation rules"""
        context = GuidelinesContext(
            property_definitions={"prop1": {"description": "Complex property"}},
            validation_rules={
                "prop1": {
                    "required": True,
                    "min_value": 0,
                    "max_value": 100,
                    "min_inclusive": True,
                    "max_inclusive": False
                }
            },
            required_properties={},
            property_types={},
            property_units={}
        )
        
        rules = context.validation_rules["prop1"]
        assert rules["required"] is True
        assert rules["min_value"] == 0
        assert rules["max_value"] == 100
        assert rules["min_inclusive"] is True
        assert rules["max_inclusive"] is False


class TestEnhancedTTLContext:
    """Test cases for EnhancedTTLContext dataclass"""

    def test_enhanced_ttl_context_creation(self):
        """Test basic EnhancedTTLContext creation"""
        mock_graph = Mock(spec=Graph)
        ontology_context = OntologyContext({}, {}, {}, {}, {})
        guidelines_context = GuidelinesContext({}, {}, {}, {}, {})
        
        context = EnhancedTTLContext(
            graph=mock_graph,
            file_path="/path/to/file.ttl",
            basic_analysis="Basic analysis text",
            ontology_context=ontology_context,
            guidelines_context=guidelines_context
        )
        
        assert context.graph == mock_graph
        assert context.file_path == "/path/to/file.ttl"
        assert context.basic_analysis == "Basic analysis text"
        assert context.ontology_context == ontology_context
        assert context.guidelines_context == guidelines_context

    def test_enhanced_ttl_context_with_real_graph(self):
        """Test EnhancedTTLContext with real Graph object"""
        graph = Graph()
        ontology_context = OntologyContext({}, {}, {}, {}, {})
        guidelines_context = GuidelinesContext({}, {}, {}, {}, {})
        
        context = EnhancedTTLContext(
            graph=graph,
            file_path="test.ttl",
            basic_analysis="Test analysis",
            ontology_context=ontology_context,
            guidelines_context=guidelines_context
        )
        
        assert isinstance(context.graph, Graph)
        assert len(context.graph) == 0  # Empty graph


class TestContextualInsight:
    """Test cases for ContextualInsight Pydantic model"""

    def test_contextual_insight_creation(self):
        """Test basic ContextualInsight creation"""
        insight = ContextualInsight(
            insight="Test insight",
            confidence=0.9,
            supporting_data="Test data",
            ontology_context="Test ontology context",
            guidelines_context="Test guidelines context"
        )
        
        assert insight.insight == "Test insight"
        assert insight.confidence == 0.9
        assert insight.supporting_data == "Test data"
        assert insight.ontology_context == "Test ontology context"
        assert insight.guidelines_context == "Test guidelines context"

    def test_contextual_insight_default_confidence(self):
        """Test ContextualInsight with default confidence"""
        insight = ContextualInsight(
            insight="Test insight",
            supporting_data="Test data",
            ontology_context="Test ontology context",
            guidelines_context="Test guidelines context"
        )
        
        assert insight.confidence == 0.8  # Default value

    def test_contextual_insight_validation_confidence_range(self):
        """Test ContextualInsight confidence validation"""
        # Valid confidence values
        insight1 = ContextualInsight(
            insight="Test", confidence=0.0, supporting_data="", 
            ontology_context="", guidelines_context=""
        )
        assert insight1.confidence == 0.0
        
        insight2 = ContextualInsight(
            insight="Test", confidence=1.0, supporting_data="", 
            ontology_context="", guidelines_context=""
        )
        assert insight2.confidence == 1.0

    def test_contextual_insight_empty_strings(self):
        """Test ContextualInsight with empty strings"""
        insight = ContextualInsight(
            insight="",
            confidence=0.5,
            supporting_data="",
            ontology_context="",
            guidelines_context=""
        )
        
        assert insight.insight == ""
        assert insight.supporting_data == ""
        assert insight.ontology_context == ""
        assert insight.guidelines_context == ""


class TestEnhancedSPARQLQuery:
    """Test cases for EnhancedSPARQLQuery Pydantic model"""

    def test_enhanced_sparql_query_creation(self):
        """Test basic EnhancedSPARQLQuery creation"""
        query = EnhancedSPARQLQuery(
            query="SELECT * WHERE { ?s ?p ?o }",
            query_type="SELECT",
            description="Test query",
            confidence=0.95,
            validation_passed=True,
            ontology_compliance="Compliant with ontology",
            guidelines_compliance="Follows guidelines"
        )
        
        assert query.query == "SELECT * WHERE { ?s ?p ?o }"
        assert query.query_type == "SELECT"
        assert query.description == "Test query"
        assert query.confidence == 0.95
        assert query.validation_passed is True
        assert query.ontology_compliance == "Compliant with ontology"
        assert query.guidelines_compliance == "Follows guidelines"

    def test_enhanced_sparql_query_defaults(self):
        """Test EnhancedSPARQLQuery with default values"""
        query = EnhancedSPARQLQuery(
            query="ASK { ?s ?p ?o }",
            query_type="ASK",
            description="Test ASK query",
            ontology_compliance="Test compliance",
            guidelines_compliance="Test guidelines"
        )
        
        assert query.confidence == 0.8  # Default
        assert query.validation_passed is False  # Default

    def test_enhanced_sparql_query_different_types(self):
        """Test EnhancedSPARQLQuery with different query types"""
        query_types = ["SELECT", "ASK", "CONSTRUCT", "DESCRIBE", "ERROR"]
        
        for qtype in query_types:
            query = EnhancedSPARQLQuery(
                query=f"{qtype} query",
                query_type=qtype,
                description=f"Test {qtype} query",
                ontology_compliance="Test",
                guidelines_compliance="Test"
            )
            assert query.query_type == qtype


class TestContextualStrategy:
    """Test cases for ContextualStrategy Pydantic model"""

    def test_contextual_strategy_creation(self):
        """Test basic ContextualStrategy creation"""
        strategy = ContextualStrategy(
            approach="Test approach",
            steps=["Step 1", "Step 2", "Step 3"],
            expected_queries=2,
            reasoning="Test reasoning",
            ontology_considerations="Test ontology considerations",
            guidelines_considerations="Test guidelines considerations"
        )
        
        assert strategy.approach == "Test approach"
        assert strategy.steps == ["Step 1", "Step 2", "Step 3"]
        assert strategy.expected_queries == 2
        assert strategy.reasoning == "Test reasoning"
        assert strategy.ontology_considerations == "Test ontology considerations"
        assert strategy.guidelines_considerations == "Test guidelines considerations"

    def test_contextual_strategy_empty_steps(self):
        """Test ContextualStrategy with empty steps list"""
        strategy = ContextualStrategy(
            approach="Test approach",
            steps=[],
            expected_queries=0,
            reasoning="No steps needed",
            ontology_considerations="None",
            guidelines_considerations="None"
        )
        
        assert strategy.steps == []
        assert strategy.expected_queries == 0

    def test_contextual_strategy_many_steps(self):
        """Test ContextualStrategy with many steps"""
        many_steps = [f"Step {i}" for i in range(1, 11)]
        strategy = ContextualStrategy(
            approach="Complex approach",
            steps=many_steps,
            expected_queries=10,
            reasoning="Complex analysis needed",
            ontology_considerations="Complex ontology",
            guidelines_considerations="Complex guidelines"
        )
        
        assert len(strategy.steps) == 10
        assert strategy.expected_queries == 10


class TestContextRequirements:
    """Test cases for ContextRequirements Pydantic model"""

    def test_context_requirements_creation(self):
        """Test basic ContextRequirements creation"""
        requirements = ContextRequirements(
            needs_ontology=True,
            needs_guidelines=False,
            needs_analysis_only=False,
            reasoning="Need ontology for relationships",
            question_type="relationship",
            confidence=0.9,
            key_concepts=["class", "property", "relationship"],
            expected_complexity="medium"
        )
        
        assert requirements.needs_ontology is True
        assert requirements.needs_guidelines is False
        assert requirements.needs_analysis_only is False
        assert requirements.reasoning == "Need ontology for relationships"
        assert requirements.question_type == "relationship"
        assert requirements.confidence == 0.9
        assert requirements.key_concepts == ["class", "property", "relationship"]
        assert requirements.expected_complexity == "medium"

    def test_context_requirements_defaults(self):
        """Test ContextRequirements with default values"""
        requirements = ContextRequirements(
            needs_ontology=False,
            needs_guidelines=True,
            needs_analysis_only=False,
            reasoning="Need guidelines for validation",
            question_type="validation",
            key_concepts=["validation", "rules"],
            expected_complexity="low"
        )
        
        assert requirements.confidence == 0.8  # Default value

    def test_context_requirements_all_contexts_needed(self):
        """Test ContextRequirements when all contexts are needed"""
        requirements = ContextRequirements(
            needs_ontology=True,
            needs_guidelines=True,
            needs_analysis_only=False,
            reasoning="Complex question needs all contexts",
            question_type="complex",
            confidence=0.7,
            key_concepts=["complex", "analysis", "comprehensive"],
            expected_complexity="high"
        )
        
        assert requirements.needs_ontology is True
        assert requirements.needs_guidelines is True
        assert requirements.needs_analysis_only is False

    def test_context_requirements_analysis_only(self):
        """Test ContextRequirements for analysis-only questions"""
        requirements = ContextRequirements(
            needs_ontology=False,
            needs_guidelines=False,
            needs_analysis_only=True,
            reasoning="Simple counting question",
            question_type="counting",
            confidence=0.95,
            key_concepts=["count", "simple"],
            expected_complexity="low"
        )
        
        assert requirements.needs_ontology is False
        assert requirements.needs_guidelines is False
        assert requirements.needs_analysis_only is True

    def test_context_requirements_empty_key_concepts(self):
        """Test ContextRequirements with empty key concepts"""
        requirements = ContextRequirements(
            needs_ontology=True,
            needs_guidelines=False,
            needs_analysis_only=False,
            reasoning="No specific concepts identified",
            question_type="exploratory",
            key_concepts=[],
            expected_complexity="medium"
        )
        
        assert requirements.key_concepts == []

    def test_context_requirements_question_types(self):
        """Test ContextRequirements with different question types"""
        question_types = ["exploratory", "counting", "validation", "relationship", "existence", "complex"]
        
        for qtype in question_types:
            requirements = ContextRequirements(
                needs_ontology=True,
                needs_guidelines=False,
                needs_analysis_only=False,
                reasoning=f"Test {qtype} question",
                question_type=qtype,
                key_concepts=[qtype],
                expected_complexity="medium"
            )
            assert requirements.question_type == qtype

    def test_context_requirements_complexity_levels(self):
        """Test ContextRequirements with different complexity levels"""
        complexity_levels = ["low", "medium", "high"]
        
        for level in complexity_levels:
            requirements = ContextRequirements(
                needs_ontology=True,
                needs_guidelines=False,
                needs_analysis_only=False,
                reasoning=f"Test {level} complexity",
                question_type="test",
                key_concepts=["test"],
                expected_complexity=level
            )
            assert requirements.expected_complexity == level
