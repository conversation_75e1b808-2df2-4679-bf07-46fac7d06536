#!/usr/bin/env python3
"""
Comprehensive test runner for all components
Tests all functions and handles edge cases without missing any part
"""

import asyncio
import json
import tempfile
import os
from unittest.mock import Mock, patch
from typing import Dict, List

# Import all components to test
from core_logic import (
    # Timing system
    TimingEntry, TimingTracker, timed_operation,
    
    # Data models
    OntologyContext, GuidelinesContext, EnhancedTTLContext,
    ContextualInsight, EnhancedSPARQLQuery, ContextualStrategy, ContextRequirements,
    
    # Analysis functions
    analyze_ontology, analyze_guidelines, analyze_ttl_file_basic,
    
    # Formatting functions
    format_class_relationships, format_property_constraints,
    format_property_definitions, format_required_properties,
    format_property_types_units,
    
    # SPARQL validation
    validate_sparql_query,
    
    # Agent tools
    analyze_context_requirements, extract_ontology_context,
    extract_guidelines_context, develop_contextual_strategy,
    generate_enhanced_sparql_query, execute_sparql_query,
    analyze_contextual_results
)

class TestRunner:
    """Comprehensive test runner"""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []

    def test(self, name: str, test_func):
        """Run a single test"""
        try:
            print(f"🧪 Testing {name}...")
            test_func()
            print(f"✅ {name} passed")
            self.passed += 1
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            self.failed += 1
            self.errors.append(f"{name}: {e}")

    async def async_test(self, name: str, test_func):
        """Run a single async test"""
        try:
            print(f"🧪 Testing {name}...")
            await test_func()
            print(f"✅ {name} passed")
            self.passed += 1
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            self.failed += 1
            self.errors.append(f"{name}: {e}")

    def test_timing_system(self):
        """Test timing system components"""
        # Test TimingEntry
        entry = TimingEntry("test", 1000.0)
        assert entry.name == "test"
        assert entry.start_time == 1000.0
        
        # Test finish
        with patch('time.time', return_value=1005.0):
            entry.finish("done")
        assert entry.duration == 5.0
        assert entry.details == "done"
        
        # Test TimingTracker
        tracker = TimingTracker()
        with patch('time.time', side_effect=[1000.0, 1002.0]):
            tracker.start_operation("op1")
            tracker.finish_operation()
        
        assert len(tracker.entries) == 1
        assert tracker.entries[0].duration == 2.0

    def test_data_models(self):
        """Test all data models"""
        # Test OntologyContext
        ontology = OntologyContext({}, {}, {}, {}, {})
        assert ontology.class_relationships == {}
        
        # Test GuidelinesContext
        guidelines = GuidelinesContext({}, {}, {}, {}, {})
        assert guidelines.property_definitions == {}
        
        # Test Pydantic models
        insight = ContextualInsight(
            insight="test",
            supporting_data="data",
            ontology_context="ontology",
            guidelines_context="guidelines"
        )
        assert insight.confidence == 0.8  # Default value

    def test_formatting_functions(self):
        """Test all formatting functions"""
        # Test format_class_relationships
        result = format_class_relationships(None)
        assert result == "No class relationships found"
        
        result = format_class_relationships({"ClassA": ["ClassB"]})
        assert "ClassA → ClassB" in result
        
        # Test format_property_constraints
        result = format_property_constraints(None)
        assert result == "No property constraints found"
        
        # Test format_property_definitions
        result = format_property_definitions(None, {})
        assert result == "No property definitions found"
        
        # Test format_required_properties
        result = format_required_properties(None)
        assert result == "No required properties found"
        
        # Test format_property_types_units
        result = format_property_types_units(None, {})
        assert result == "No property type information found"

    def test_sparql_validation(self):
        """Test SPARQL validation"""
        # Test valid queries
        assert validate_sparql_query("SELECT * WHERE { ?s ?p ?o }") == "SELECT"
        assert validate_sparql_query("ASK { ?s ?p ?o }") == "ASK"
        
        # Test invalid queries
        try:
            validate_sparql_query("")
            assert False, "Should have raised ValueError"
        except ValueError:
            pass  # Expected
        
        try:
            validate_sparql_query(None)
            assert False, "Should have raised ValueError"
        except ValueError:
            pass  # Expected

    async def test_analyze_contextual_results_fix(self):
        """Test the specific fix for 'list' object has no attribute 'get' error"""
        mock_context = Mock()
        mock_context.deps = Mock()
        
        # Test list input (this was causing the error)
        result = await analyze_contextual_results(
            mock_context,
            [{'name': 'test', 'count': 5}],
            'How many items?'
        )
        assert isinstance(result, ContextualInsight)
        assert "Found 1 results" in result.insight
        
        # Test dict input
        result = await analyze_contextual_results(
            mock_context,
            {'success': True, 'data': [{'count': 10}], 'row_count': 1},
            'How many items?'
        )
        assert isinstance(result, ContextualInsight)
        
        # Test JSON string input
        result = await analyze_contextual_results(
            mock_context,
            json.dumps({'success': True, 'data': [], 'row_count': 0}),
            'Test question'
        )
        assert isinstance(result, ContextualInsight)
        
        # Test invalid input
        result = await analyze_contextual_results(
            mock_context,
            123,  # Invalid type
            'Test question'
        )
        assert isinstance(result, ContextualInsight)
        assert "Unexpected query result type" in result.insight

    async def test_agent_tools(self):
        """Test agent tools with mocking"""
        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.ontology_context = OntologyContext({}, {}, {}, {}, {})
        mock_context.deps.guidelines_context = GuidelinesContext({}, {}, {}, {}, {})
        
        # Test extract_ontology_context
        result = await extract_ontology_context(mock_context, "test question")
        assert isinstance(result, str)
        
        # Test extract_guidelines_context
        result = await extract_guidelines_context(mock_context, "test question")
        assert isinstance(result, str)
        
        # Test develop_contextual_strategy
        result = await develop_contextual_strategy(mock_context, "test question")
        assert isinstance(result, ContextualStrategy)

    def test_analysis_functions_edge_cases(self):
        """Test analysis functions with edge cases"""
        # Test with non-existent files
        result = analyze_ontology("/nonexistent/file.ttl")
        assert isinstance(result, OntologyContext)
        assert result.class_relationships == {}
        
        result = analyze_guidelines("/nonexistent/file.json")
        assert isinstance(result, GuidelinesContext)
        assert result.property_definitions == {}
        
        result = analyze_ttl_file_basic("/nonexistent/file.ttl")
        assert isinstance(result, str)
        assert "Error" in result

    def create_temp_file(self, content: str, suffix: str = ".txt") -> str:
        """Create a temporary file with content"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False)
        temp_file.write(content)
        temp_file.close()
        return temp_file.name

    def test_analysis_functions_with_files(self):
        """Test analysis functions with actual files"""
        # Test ontology analysis with valid TTL
        ontology_content = """
        @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
        @prefix ibpdi: <https://ibpdi.datacat.org/class/> .
        
        ibpdi:hasProperty rdfs:domain ibpdi:ClassA .
        ibpdi:hasProperty rdfs:range ibpdi:ClassB .
        """
        
        temp_file = self.create_temp_file(ontology_content, ".ttl")
        try:
            result = analyze_ontology(temp_file)
            assert isinstance(result, OntologyContext)
            assert "hasProperty" in result.property_domains
        finally:
            os.unlink(temp_file)
        
        # Test guidelines analysis with valid JSON
        guidelines_data = {
            "Domain": {
                "Classifications": {
                    "$values": [
                        {
                            "Name": "TestClass",
                            "ClassificationProperties": {
                                "$values": [
                                    {
                                        "Name": "TestProperty",
                                        "Description": "A test property",
                                        "IsRequired": True,
                                        "PropertyAssignment": {
                                            "Property": {"StorageType": 1}
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        }
        
        temp_file = self.create_temp_file(json.dumps(guidelines_data), ".json")
        try:
            result = analyze_guidelines(temp_file)
            assert isinstance(result, GuidelinesContext)
            assert "TestProperty" in result.property_definitions
        finally:
            os.unlink(temp_file)

    async def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting comprehensive test suite...")
        print("=" * 80)
        
        # Synchronous tests
        self.test("Timing System", self.test_timing_system)
        self.test("Data Models", self.test_data_models)
        self.test("Formatting Functions", self.test_formatting_functions)
        self.test("SPARQL Validation", self.test_sparql_validation)
        self.test("Analysis Functions Edge Cases", self.test_analysis_functions_edge_cases)
        self.test("Analysis Functions with Files", self.test_analysis_functions_with_files)
        
        # Asynchronous tests
        await self.async_test("Analyze Contextual Results Fix", self.test_analyze_contextual_results_fix)
        await self.async_test("Agent Tools", self.test_agent_tools)
        
        # Print summary
        print("=" * 80)
        print(f"📊 Test Summary:")
        print(f"✅ Passed: {self.passed}")
        print(f"❌ Failed: {self.failed}")
        print(f"📈 Success Rate: {self.passed / (self.passed + self.failed) * 100:.1f}%")
        
        if self.errors:
            print("\n❌ Errors:")
            for error in self.errors:
                print(f"  - {error}")
        
        if self.failed == 0:
            print("\n🎉 All tests passed! The code is working correctly.")
        else:
            print(f"\n⚠️  {self.failed} tests failed. Please review the errors above.")
        
        return self.failed == 0

async def main():
    """Main test runner"""
    runner = TestRunner()
    success = await runner.run_all_tests()
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
