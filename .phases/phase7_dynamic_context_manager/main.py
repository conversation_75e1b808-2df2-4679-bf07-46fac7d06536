#!/usr/bin/env python3
"""
Dynamic Context Manager for TTL Question-Answering System
Enhanced agentic system with intelligent ontology and guidelines-based context extraction
"""

# Import the core logic and CLI
try:
    from .core_logic import *
    from .cli import EnhancedTTLQACLI
except ImportError:
    # Fallback for direct execution
    from core_logic import *
    from cli import EnhancedTTLQACLI

def main():
    """Main entry point for the enhanced TTL QA system"""
    app = EnhancedTTLQACLI()
    try:
        app.run()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Application error: {e}")

if __name__ == "__main__":
    main()
