"""
CLI Module for TTL Question-Answering System
Contains the command-line interface and Rich console functionality
"""

import asyncio
from pathlib import Path
from typing import Optional

# Rich imports for beautiful CLI
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt
from rich.table import Table
from rich.markdown import Markdown
from rich import box
from rich.align import Align

# RDF handling
from rdflib import Graph

# Import core logic
try:
    from .core_logic import (
        EnhancedTTLContext,
        analyze_ontology,
        analyze_guidelines,
        analyze_ttl_file_basic,
        enhanced_ttl_qa_agent,
        timing_tracker,
        TimingTracker,
        timed_operation
    )
except ImportError:
    # Fallback for direct execution
    from core_logic import (
        EnhancedTTLContext,
        analyze_ontology,
        analyze_guidelines,
        analyze_ttl_file_basic,
        enhanced_ttl_qa_agent,
        timing_tracker,
        TimingTracker,
        timed_operation
    )

class EnhancedTTLQACLI:
    """Enhanced TTL Question-Answering CLI with Dynamic Context Management"""

    def __init__(self):
        self.console = Console()
        self.current_context: Optional[EnhancedTTLContext] = None

    def print_header(self):
        """Print beautiful app header"""
        header_text = """
🚀 Enhanced TTL Question-Answering System
Dynamic Context Manager with Ontology & Guidelines Intelligence
        """

        header_panel = Panel(
            Align.center(header_text),
            border_style="bright_blue",
            box=box.DOUBLE_EDGE,
            padding=(1, 2)
        )

        self.console.print(header_panel)
        self.console.print()

    def print_status(self):
        """Print current system status"""
        if self.current_context:
            status_table = Table(show_header=False, box=box.MINIMAL)
            status_table.add_column("Item", style="cyan")
            status_table.add_column("Value", style="green")

            status_table.add_row("📁 Current File", Path(self.current_context.file_path).name)
            status_table.add_row("🔢 Total Triples", str(len(self.current_context.graph)))
            status_table.add_row("🏗️ Ontology Classes", str(len(self.current_context.ontology_context.class_relationships)))
            status_table.add_row("📋 Guidelines Properties", str(len(self.current_context.guidelines_context.property_definitions)))

            status_panel = Panel(
                status_table,
                title="📊 Enhanced System Status",
                border_style="green",
                padding=(0, 1)
            )

            self.console.print(status_panel)
        else:
            self.console.print(Panel(
                "[yellow]No TTL file loaded. Use 'load' command to load a file with ontology and guidelines.[/yellow]",
                title="⚠️ Status",
                border_style="yellow"
            ))

    @timed_operation("Load Enhanced TTL File", "operation")
    def load_enhanced_ttl_file(self) -> bool:
        """Load a TTL file with ontology and guidelines for enhanced context"""
        self.console.print("\n[bold cyan]Loading Enhanced TTL File with Context[/bold cyan]")

        # Get file paths
        default_ttl = "/home/<USER>/dev/BA/.phases/phase7_dynamic_context_manager/assets/example.ttl"
        default_ontology = "/home/<USER>/dev/BA/.phases/phase7_dynamic_context_manager/assets/ontology.ttl"
        default_guidelines = "/home/<USER>/dev/BA/.phases/phase7_dynamic_context_manager/assets/guideline.json"

        ttl_path = Prompt.ask(
            "[cyan]Enter TTL file path[/cyan]",
            default=default_ttl,
            console=self.console
        )

        ontology_path = Prompt.ask(
            "[cyan]Enter ontology file path[/cyan]",
            default=default_ontology,
            console=self.console
        )

        guidelines_path = Prompt.ask(
            "[cyan]Enter guidelines file path[/cyan]",
            default=default_guidelines,
            console=self.console
        )

        # Validate file existence
        for path, name in [(ttl_path, "TTL"), (ontology_path, "Ontology"), (guidelines_path, "Guidelines")]:
            if not Path(path).exists():
                self.console.print(f"[red]❌ {name} file not found: {path}[/red]")
                return False

        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("Loading TTL file...", total=None)

                # Load TTL graph
                graph = Graph()
                graph.parse(ttl_path, format='turtle')

                progress.update(task, description="Analyzing ontology...")
                ontology_context = analyze_ontology(ontology_path)

                progress.update(task, description="Processing guidelines...")
                guidelines_context = analyze_guidelines(guidelines_path)

                progress.update(task, description="Building enhanced analysis...")
                basic_analysis = analyze_ttl_file_basic(ttl_path)

                self.current_context = EnhancedTTLContext(
                    graph=graph,
                    file_path=ttl_path,
                    basic_analysis=basic_analysis,
                    ontology_context=ontology_context,
                    guidelines_context=guidelines_context
                )

                progress.update(task, description="✅ Enhanced TTL context loaded successfully!")

            self.console.print(f"[green]✅ Successfully loaded enhanced TTL context with {len(graph)} triples[/green]")
            self.console.print(f"[green]📊 Ontology: {len(ontology_context.class_relationships)} class relationships[/green]")
            self.console.print(f"[green]📋 Guidelines: {len(guidelines_context.property_definitions)} property definitions[/green]")
            return True

        except Exception as e:
            self.console.print(f"[red]❌ Error loading enhanced TTL context: {e}[/red]")
            return False

    @timed_operation("Ask Enhanced Question", "operation")
    async def ask_enhanced_question(self, question: str):
        """Process a user question using the enhanced agentic approach with dynamic context"""
        # Reset timing tracker for clean timing data per question
        global timing_tracker
        timing_tracker = TimingTracker()

        if not self.current_context:
            self.console.print("[red]❌ No enhanced TTL context loaded. Please load files first.[/red]")
            return

        self.console.print(Panel(
            f"[bold cyan]Question:[/bold cyan] {question}",
            title="❓ Enhanced User Query",
            border_style="blue"
        ))

        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console,
                transient=False
            ) as progress:
                task = progress.add_task("🤖 Enhanced agent processing with dynamic context...", total=None)

                # Run the enhanced agent with full context
                result = await enhanced_ttl_qa_agent.run(
                    f"Please answer this question about the TTL data using full contextual analysis: {question}",
                    deps=self.current_context
                )

                progress.update(task, description="✅ Enhanced agent completed!", completed=True)

            # Display the enhanced response
            self.console.print(Panel(
                result.output,
                title="🚀 Enhanced Contextual Response",
                border_style="green",
                padding=(1, 2)
            ))

            # Show usage information
            usage = result.usage()
            if usage:
                usage_info = f"**Requests:** {usage.requests} | **Tokens:** {usage.total_tokens}"
                self.console.print(Panel(
                    Markdown(usage_info),
                    title="📈 Usage Stats",
                    border_style="dim"
                ))

            # Display detailed timing report
            timing_tracker.display_timing_report(self.console)

        except Exception as e:
            self.console.print(f"[red]❌ Error processing enhanced question: {e}[/red]")
            # Still show timing report even on error
            timing_tracker.display_timing_report(self.console)

    def run(self):
        """Run the enhanced CLI application"""
        self.print_header()

        while True:
            self.print_status()
            self.console.print()

            # Show available commands
            commands_table = Table(show_header=True, header_style="bold magenta")
            commands_table.add_column("Command", style="cyan")
            commands_table.add_column("Description", style="white")

            commands_table.add_row("load", "Load TTL file with ontology and guidelines")
            commands_table.add_row("ask", "Ask a question about the data")
            commands_table.add_row("context", "Show current context information")
            commands_table.add_row("quit", "Exit the application")

            self.console.print(Panel(
                commands_table,
                title="🛠️ Available Commands",
                border_style="magenta"
            ))

            command = Prompt.ask(
                "\n[bold yellow]Enter command[/bold yellow]",
                choices=["load", "ask", "context", "quit"],
                default="ask" if self.current_context else "load",
                console=self.console
            )

            if command == "load":
                self.load_enhanced_ttl_file()

            elif command == "ask":
                if not self.current_context:
                    self.console.print("[red]❌ Please load TTL files first using 'load' command[/red]")
                    continue

                question = Prompt.ask(
                    "[cyan]Enter your question about the TTL data[/cyan]",
                    console=self.console
                )

                if question.strip():
                    asyncio.run(self.ask_enhanced_question(question))

            elif command == "context":
                if self.current_context:
                    self.console.print(Panel(
                        self.current_context.basic_analysis,
                        title="📊 Current Context Analysis",
                        border_style="blue"
                    ))
                else:
                    self.console.print("[yellow]No context loaded[/yellow]")

            elif command == "quit":
                self.console.print("[green]👋 Goodbye![/green]")
                break
