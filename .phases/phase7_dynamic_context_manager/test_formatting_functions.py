"""
Comprehensive unit tests for formatting functions
Tests all format_* functions handling None values, empty data, and malformed data
"""

import pytest
from typing import Dict, List

# Import the formatting functions
from core_logic import (
    format_class_relationships,
    format_property_constraints,
    format_property_definitions,
    format_required_properties,
    format_property_types_units
)


class TestFormatClassRelationships:
    """Test cases for format_class_relationships function"""

    def test_format_class_relationships_basic(self):
        """Test basic class relationships formatting"""
        relationships = {
            "ClassA": ["ClassB", "ClassC"],
            "ClassB": ["ClassD"],
            "ClassC": []
        }
        
        result = format_class_relationships(relationships)
        
        assert "- ClassA → ClassB, ClassC" in result
        assert "- ClassB → ClassD" in result
        # Empty relationships should not appear or should be handled gracefully

    def test_format_class_relationships_empty_dict(self):
        """Test formatting with empty dictionary"""
        result = format_class_relationships({})
        assert result == "No class relationships found"

    def test_format_class_relationships_none(self):
        """Test formatting with None input"""
        result = format_class_relationships(None)
        assert result == "No class relationships found"

    def test_format_class_relationships_empty_lists(self):
        """Test formatting with empty lists as values"""
        relationships = {
            "ClassA": [],
            "ClassB": [],
        }
        
        result = format_class_relationships(relationships)
        # Should handle empty lists gracefully
        assert isinstance(result, str)

    def test_format_class_relationships_none_values_in_list(self):
        """Test formatting with None values in relationship lists"""
        relationships = {
            "ClassA": [None, "ClassB", None],
            "ClassB": ["ClassC", None]
        }
        
        result = format_class_relationships(relationships)
        
        # Should handle None values by converting to string
        assert "- ClassA → None, ClassB, None" in result
        assert "- ClassB → ClassC, None" in result

    def test_format_class_relationships_non_list_values(self):
        """Test formatting with non-list values (edge case)"""
        relationships = {
            "ClassA": "NotAList",
            "ClassB": 123,
            "ClassC": None
        }
        
        result = format_class_relationships(relationships)
        
        # Should handle gracefully and not crash
        assert isinstance(result, str)
        # Non-list values should be skipped

    def test_format_class_relationships_large_dataset(self):
        """Test formatting with large dataset (truncation)"""
        relationships = {}
        for i in range(15):  # More than 10 items
            relationships[f"Class{i}"] = [f"RelatedClass{i}"]
        
        result = format_class_relationships(relationships)
        
        # Should truncate after 10 items
        assert "..." in result
        lines = result.split('\n')
        # Should have 10 relationship lines plus "..."
        assert len([line for line in lines if line.startswith('-')]) == 10

    def test_format_class_relationships_exception_handling(self):
        """Test formatting with data that causes exceptions"""
        # Create a mock object that raises AttributeError when accessed
        class BadDict(dict):
            def items(self):
                raise AttributeError("Test error")
        
        bad_relationships = BadDict()
        result = format_class_relationships(bad_relationships)
        
        assert result == "Error formatting class relationships"

    def test_format_class_relationships_mixed_types(self):
        """Test formatting with mixed data types"""
        relationships = {
            "ClassA": ["ClassB", 123, None, "ClassC"],
            "ClassB": [True, False, "ClassD"],
            123: ["ShouldWork"]  # Non-string key
        }
        
        result = format_class_relationships(relationships)
        
        # Should handle mixed types by converting to strings
        assert isinstance(result, str)
        assert "ClassA" in result
        assert "ClassB" in result


class TestFormatPropertyConstraints:
    """Test cases for format_property_constraints function"""

    def test_format_property_constraints_basic(self):
        """Test basic property constraints formatting"""
        constraints = {
            "prop1": {
                "domains": ["ClassA", "ClassB"],
                "ranges": ["ClassC", "ClassD"]
            },
            "prop2": {
                "domains": ["ClassE"],
                "ranges": ["ClassF"]
            }
        }
        
        result = format_property_constraints(constraints)
        
        assert "- prop1: ClassA, ClassB → ClassC, ClassD" in result
        assert "- prop2: ClassE → ClassF" in result

    def test_format_property_constraints_empty_dict(self):
        """Test formatting with empty dictionary"""
        result = format_property_constraints({})
        assert result == "No property constraints found"

    def test_format_property_constraints_none(self):
        """Test formatting with None input"""
        result = format_property_constraints(None)
        assert result == "No property constraints found"

    def test_format_property_constraints_missing_keys(self):
        """Test formatting with missing domains/ranges keys"""
        constraints = {
            "prop1": {},  # Missing domains and ranges
            "prop2": {"domains": ["ClassA"]},  # Missing ranges
            "prop3": {"ranges": ["ClassB"]}  # Missing domains
        }
        
        result = format_property_constraints(constraints)
        
        # Should handle missing keys gracefully with empty strings
        assert "- prop1:  → " in result
        assert "- prop2: ClassA → " in result
        assert "- prop3:  → ClassB" in result

    def test_format_property_constraints_non_dict_values(self):
        """Test formatting with non-dict constraint values"""
        constraints = {
            "prop1": "NotADict",
            "prop2": None,
            "prop3": 123
        }
        
        result = format_property_constraints(constraints)
        
        # Should skip non-dict values
        assert isinstance(result, str)
        # May be empty or have error handling

    def test_format_property_constraints_empty_lists(self):
        """Test formatting with empty domain/range lists"""
        constraints = {
            "prop1": {
                "domains": [],
                "ranges": []
            }
        }
        
        result = format_property_constraints(constraints)
        
        assert "- prop1:  → " in result

    def test_format_property_constraints_none_in_lists(self):
        """Test formatting with None values in domain/range lists"""
        constraints = {
            "prop1": {
                "domains": [None, "ClassA", None],
                "ranges": ["ClassB", None]
            }
        }
        
        result = format_property_constraints(constraints)
        
        # Should handle None by converting to string
        assert "- prop1: None, ClassA, None → ClassB, None" in result

    def test_format_property_constraints_large_dataset(self):
        """Test formatting with large dataset (truncation)"""
        constraints = {}
        for i in range(15):  # More than 10 items
            constraints[f"prop{i}"] = {
                "domains": [f"Domain{i}"],
                "ranges": [f"Range{i}"]
            }
        
        result = format_property_constraints(constraints)
        
        # Should truncate after 10 items
        assert "..." in result

    def test_format_property_constraints_exception_handling(self):
        """Test formatting with data that causes exceptions"""
        class BadDict(dict):
            def items(self):
                raise TypeError("Test error")
        
        bad_constraints = BadDict()
        result = format_property_constraints(bad_constraints)
        
        assert result == "Error formatting property constraints"


class TestFormatPropertyDefinitions:
    """Test cases for format_property_definitions function"""

    def test_format_property_definitions_basic(self):
        """Test basic property definitions formatting"""
        definitions = {
            "prop1": {"description": "First property"},
            "prop2": {"description": "Second property"}
        }
        rules = {
            "prop1": {"required": True},
            "prop2": {"required": False}
        }
        
        result = format_property_definitions(definitions, rules)
        
        assert "- prop1: First property (Required)" in result
        assert "- prop2: Second property (Optional)" in result

    def test_format_property_definitions_empty_definitions(self):
        """Test formatting with empty definitions"""
        result = format_property_definitions({}, {})
        assert result == "No property definitions found"

    def test_format_property_definitions_none_definitions(self):
        """Test formatting with None definitions"""
        result = format_property_definitions(None, {})
        assert result == "No property definitions found"

    def test_format_property_definitions_none_rules(self):
        """Test formatting with None rules"""
        definitions = {
            "prop1": {"description": "Test property"}
        }
        
        result = format_property_definitions(definitions, None)
        
        # Should handle None rules gracefully
        assert "- prop1: Test property (Optional)" in result

    def test_format_property_definitions_missing_description(self):
        """Test formatting with missing description"""
        definitions = {
            "prop1": {},  # Missing description
            "prop2": {"description": ""}  # Empty description
        }
        rules = {}
        
        result = format_property_definitions(definitions, rules)
        
        assert "- prop1: No description (Optional)" in result
        assert "- prop2:  (Optional)" in result

    def test_format_property_definitions_missing_rules(self):
        """Test formatting with missing rules for some properties"""
        definitions = {
            "prop1": {"description": "First property"},
            "prop2": {"description": "Second property"}
        }
        rules = {
            "prop1": {"required": True}
            # prop2 missing from rules
        }
        
        result = format_property_definitions(definitions, rules)
        
        assert "- prop1: First property (Required)" in result
        assert "- prop2: Second property (Optional)" in result

    def test_format_property_definitions_non_dict_values(self):
        """Test formatting with non-dict definition values"""
        definitions = {
            "prop1": "NotADict",
            "prop2": None,
            "prop3": {"description": "Valid property"}
        }
        rules = {}
        
        result = format_property_definitions(definitions, rules)
        
        # Should skip non-dict values and process valid ones
        assert "- prop3: Valid property (Optional)" in result

    def test_format_property_definitions_non_dict_rules(self):
        """Test formatting with non-dict rules"""
        definitions = {
            "prop1": {"description": "Test property"}
        }
        rules = "NotADict"
        
        result = format_property_definitions(definitions, rules)
        
        # Should handle gracefully
        assert "- prop1: Test property (Optional)" in result

    def test_format_property_definitions_large_dataset(self):
        """Test formatting with large dataset (truncation)"""
        definitions = {}
        rules = {}
        for i in range(15):  # More than 10 items
            definitions[f"prop{i}"] = {"description": f"Property {i}"}
            rules[f"prop{i}"] = {"required": i % 2 == 0}
        
        result = format_property_definitions(definitions, rules)
        
        # Should truncate after 10 items
        assert "..." in result

    def test_format_property_definitions_exception_handling(self):
        """Test formatting with data that causes exceptions"""
        class BadDict(dict):
            def items(self):
                raise AttributeError("Test error")
        
        bad_definitions = BadDict()
        result = format_property_definitions(bad_definitions, {})
        
        assert result == "Error formatting property definitions"


class TestFormatRequiredProperties:
    """Test cases for format_required_properties function"""

    def test_format_required_properties_basic(self):
        """Test basic required properties formatting"""
        required_props = {
            "ClassA": ["prop1", "prop2"],
            "ClassB": ["prop3"],
            "ClassC": []
        }
        
        result = format_required_properties(required_props)
        
        assert "- ClassA: prop1, prop2" in result
        assert "- ClassB: prop3" in result
        # Empty lists should be handled gracefully

    def test_format_required_properties_empty_dict(self):
        """Test formatting with empty dictionary"""
        result = format_required_properties({})
        assert result == "No required properties found"

    def test_format_required_properties_none(self):
        """Test formatting with None input"""
        result = format_required_properties(None)
        assert result == "No required properties found"

    def test_format_required_properties_empty_lists(self):
        """Test formatting with empty property lists"""
        required_props = {
            "ClassA": [],
            "ClassB": []
        }
        
        result = format_required_properties(required_props)
        
        # Should handle empty lists gracefully
        assert isinstance(result, str)

    def test_format_required_properties_none_in_lists(self):
        """Test formatting with None values in property lists"""
        required_props = {
            "ClassA": [None, "prop1", None],
            "ClassB": ["prop2", None]
        }
        
        result = format_required_properties(required_props)
        
        # Should handle None by converting to string
        assert "- ClassA: None, prop1, None" in result
        assert "- ClassB: prop2, None" in result

    def test_format_required_properties_non_list_values(self):
        """Test formatting with non-list values"""
        required_props = {
            "ClassA": "NotAList",
            "ClassB": None,
            "ClassC": 123
        }
        
        result = format_required_properties(required_props)
        
        # Should skip non-list values
        assert isinstance(result, str)

    def test_format_required_properties_large_dataset(self):
        """Test formatting with large dataset (truncation)"""
        required_props = {}
        for i in range(10):  # More than 5 items
            required_props[f"Class{i}"] = [f"prop{i}"]
        
        result = format_required_properties(required_props)
        
        # Should truncate after 5 items
        assert "..." in result

    def test_format_required_properties_exception_handling(self):
        """Test formatting with data that causes exceptions"""
        class BadDict(dict):
            def items(self):
                raise TypeError("Test error")
        
        bad_props = BadDict()
        result = format_required_properties(bad_props)
        
        assert result == "Error formatting required properties"


class TestFormatPropertyTypesUnits:
    """Test cases for format_property_types_units function"""

    def test_format_property_types_units_basic(self):
        """Test basic property types and units formatting"""
        types = {
            "prop1": "integer",
            "prop2": "string",
            "prop3": "float"
        }
        units = {
            "prop1": "meters (m)",
            "prop3": "kilograms (kg)"
            # prop2 has no units
        }
        
        result = format_property_types_units(types, units)
        
        assert "- prop1: integer [meters (m)]" in result
        assert "- prop2: string" in result  # No units
        assert "- prop3: float [kilograms (kg)]" in result

    def test_format_property_types_units_empty_types(self):
        """Test formatting with empty types"""
        result = format_property_types_units({}, {})
        assert result == "No property type information found"

    def test_format_property_types_units_none_types(self):
        """Test formatting with None types"""
        result = format_property_types_units(None, {})
        assert result == "No property type information found"

    def test_format_property_types_units_none_units(self):
        """Test formatting with None units"""
        types = {
            "prop1": "integer",
            "prop2": "string"
        }
        
        result = format_property_types_units(types, None)
        
        # Should handle None units gracefully
        assert "- prop1: integer" in result
        assert "- prop2: string" in result

    def test_format_property_types_units_empty_units(self):
        """Test formatting with empty units dictionary"""
        types = {
            "prop1": "integer",
            "prop2": "string"
        }
        units = {}
        
        result = format_property_types_units(types, units)
        
        assert "- prop1: integer" in result
        assert "- prop2: string" in result

    def test_format_property_types_units_non_dict_units(self):
        """Test formatting with non-dict units"""
        types = {
            "prop1": "integer"
        }
        units = "NotADict"
        
        result = format_property_types_units(types, units)
        
        # Should handle gracefully
        assert "- prop1: integer" in result

    def test_format_property_types_units_empty_unit_values(self):
        """Test formatting with empty unit values"""
        types = {
            "prop1": "integer",
            "prop2": "string"
        }
        units = {
            "prop1": "",  # Empty unit
            "prop2": None  # None unit
        }
        
        result = format_property_types_units(types, units)
        
        # Should not show brackets for empty/None units
        assert "- prop1: integer" in result
        assert "- prop2: string" in result

    def test_format_property_types_units_large_dataset(self):
        """Test formatting with large dataset (truncation)"""
        types = {}
        units = {}
        for i in range(15):  # More than 10 items
            types[f"prop{i}"] = "string"
            units[f"prop{i}"] = f"unit{i}"
        
        result = format_property_types_units(types, units)
        
        # Should truncate after 10 items
        assert "..." in result

    def test_format_property_types_units_exception_handling(self):
        """Test formatting with data that causes exceptions"""
        class BadDict(dict):
            def items(self):
                raise AttributeError("Test error")
        
        bad_types = BadDict()
        result = format_property_types_units(bad_types, {})
        
        assert result == "Error formatting property types and units"
