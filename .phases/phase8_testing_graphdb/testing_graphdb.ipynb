{"cells": [{"cell_type": "markdown", "id": "645ef735", "metadata": {}, "source": ["# GraphDB Connection with RDFlib\n", "\n", "This notebook demonstrates connecting to GraphDB using RDFlib's SPARQLStore for read-only access.\n", "The connection is tested against the 'Portfolio' repository."]}, {"cell_type": "code", "execution_count": 24, "id": "2210e66b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RDFlib version: 7.1.4\n", "GraphDB Endpoint: http://localhost:7200/repositories/Portfolio\n"]}], "source": ["# Import required libraries\n", "import rdflib\n", "from rdflib import Graph\n", "from rdflib.plugins.stores import sparqlstore\n", "\n", "print(f\"RDFlib version: {rdflib.__version__}\")\n", "\n", "# Direct connection to Portfolio repository\n", "GRAPHDB_HOST = \"localhost\"\n", "GRAPHDB_PORT = \"7200\"\n", "REPOSITORY_NAME = \"Portfolio\"\n", "\n", "query_endpoint = f\"http://{GRAPHDB_HOST}:{GRAPHDB_PORT}/repositories/{REPOSITORY_NAME}\"\n", "print(f\"GraphDB Endpoint: {query_endpoint}\")"]}, {"cell_type": "code", "execution_count": 25, "id": "dca788e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Successfully connected to GraphDB Portfolio repository\n", "Connected to: http://localhost:7200/repositories/Portfolio\n"]}], "source": ["# Create SPARQLStore connection to GraphDB\n", "store = sparqlstore.SPARQLStore()\n", "store.open(query_endpoint)\n", "\n", "# Create a graph using the store\n", "g = Graph(store)\n", "\n", "print(\"✓ Successfully connected to GraphDB Portfolio repository\")\n", "print(f\"Connected to: {query_endpoint}\")"]}, {"cell_type": "code", "execution_count": 26, "id": "32dd142e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total triples in Portfolio repository: 2270\n"]}], "source": ["# Test 1: Count total triples in the repository\n", "query = \"\"\"\n", "SELECT (COUNT(*) as ?count) \n", "WHERE {\n", "    ?s ?p ?o\n", "}\n", "\"\"\"\n", "\n", "results = g.query(query)\n", "\n", "for row in results:\n", "    triple_count = str(row['count'])\n", "    print(f\"Total triples in Portfolio repository: {triple_count}\")"]}, {"cell_type": "code", "execution_count": 27, "id": "f5800b62", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample triples from the repository:\n", "------------------------------------------------------------\n", "1. Subject: http://www.w3.org/1999/02/22-rdf-syntax-ns#type\n", "   Predicate: http://www.w3.org/1999/02/22-rdf-syntax-ns#type\n", "   Object: http://www.w3.org/1999/02/22-rdf-syntax-ns#Property\n", "\n", "2. Subject: http://www.w3.org/2000/01/rdf-schema#subPropertyOf\n", "   Predicate: http://www.w3.org/1999/02/22-rdf-syntax-ns#type\n", "   Object: http://www.w3.org/1999/02/22-rdf-syntax-ns#Property\n", "\n", "3. Subject: http://www.w3.org/2000/01/rdf-schema#subPropertyOf\n", "   Predicate: http://www.w3.org/1999/02/22-rdf-syntax-ns#type\n", "   Object: http://www.w3.org/2002/07/owl#TransitiveProperty\n", "\n", "4. Subject: http://www.w3.org/2000/01/rdf-schema#subClassOf\n", "   Predicate: http://www.w3.org/1999/02/22-rdf-syntax-ns#type\n", "   Object: http://www.w3.org/1999/02/22-rdf-syntax-ns#Property\n", "\n", "5. Subject: http://www.w3.org/2000/01/rdf-schema#subClassOf\n", "   Predicate: http://www.w3.org/1999/02/22-rdf-syntax-ns#type\n", "   Object: http://www.w3.org/2002/07/owl#TransitiveProperty\n", "\n"]}], "source": ["# Test 2: Retrieve sample triples\n", "sample_query = \"\"\"\n", "SELECT ?s ?p ?o \n", "WHERE {\n", "    ?s ?p ?o\n", "}\n", "LIMIT 5\n", "\"\"\"\n", "\n", "results = g.query(sample_query)\n", "\n", "print(\"Sample triples from the repository:\")\n", "print(\"-\" * 60)\n", "\n", "for i, row in enumerate(results, 1):\n", "    print(f\"{i}. Subject: {row['s']}\")\n", "    print(f\"   Predicate: {row['p']}\")\n", "    print(f\"   Object: {row['o']}\")\n", "    print()"]}, {"cell_type": "code", "execution_count": 28, "id": "3b916458", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample subjects from the repository:\n", "1. http://www.w3.org/1999/02/22-rdf-syntax-ns#type\n", "2. http://www.w3.org/2000/01/rdf-schema#subPropertyOf\n", "3. http://www.w3.org/2000/01/rdf-schema#subPropertyOf\n", "4. http://www.w3.org/2000/01/rdf-schema#subClassOf\n", "5. http://www.w3.org/2000/01/rdf-schema#subClassOf\n"]}], "source": ["# Test 3: Basic query with different selection\n", "basic_query = \"SELECT ?s WHERE { ?s ?p ?o } LIMIT 5\"\n", "\n", "results = g.query(basic_query)\n", "\n", "print(\"Sample subjects from the repository:\")\n", "for i, row in enumerate(results, 1):\n", "    print(f\"{i}. {row['s']}\")"]}, {"cell_type": "code", "execution_count": 29, "id": "7d6aeee4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample predicates used in the repository:\n", "1. http://www.w3.org/1999/02/22-rdf-syntax-ns#type\n", "2. http://www.w3.org/2000/01/rdf-schema#subPropertyOf\n", "3. http://www.w3.org/2000/01/rdf-schema#subClassOf\n", "4. http://www.w3.org/2000/01/rdf-schema#domain\n", "5. http://www.w3.org/2000/01/rdf-schema#range\n", "6. http://proton.semanticweb.org/protonsys#transitiveOver\n", "7. http://www.w3.org/2002/07/owl#inverseOf\n", "8. https://ibpdi.datacat.org/class/hasBuilding\n", "9. https://ibpdi.datacat.org/property/city\n", "10. https://ibpdi.datacat.org/property/country\n"]}], "source": ["# Test 4: Query with specific patterns\n", "pattern_query = \"\"\"\n", "SELECT DISTINCT ?predicate\n", "WHERE {\n", "    ?s ?predicate ?o\n", "}\n", "LIMIT 10\n", "\"\"\"\n", "\n", "results = g.query(pattern_query)\n", "\n", "print(\"Sample predicates used in the repository:\")\n", "for i, row in enumerate(results, 1):\n", "    print(f\"{i}. {row['predicate']}\")"]}, {"cell_type": "code", "execution_count": 30, "id": "36656c5c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Store connection closed\n", "\n", "============================================================\n", "GRAPHDB-RDFLIB CONNECTION SUMMARY\n", "============================================================\n", "✅ Successfully connected RDFlib to GraphDB\n", "✅ SPARQLStore works seamlessly with GraphDB\n", "✅ All basic SPARQL queries function properly\n", "✅ Repository: Portfolio\n", "✅ Read-only operations fully supported\n", "\n", "🎯 Key takeaway: RDFlib + GraphDB = Perfect integration!\n", "   Use SPARQLStore for read access to GraphDB repositories.\n"]}], "source": ["# Clean up: Close the store connection\n", "store.close()\n", "print(\"✓ Store connection closed\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"GRAPHDB-RDFLIB CONNECTION SUMMARY\")\n", "print(\"=\"*60)\n", "print(\"✅ Successfully connected RDFlib to GraphDB\")\n", "print(\"✅ SPARQLStore works seamlessly with GraphDB\")\n", "print(\"✅ All basic SPARQL queries function properly\")\n", "print(\"✅ Repository: Portfolio\")\n", "print(\"✅ Read-only operations fully supported\")\n", "print(\"\\n🎯 Key takeaway: RDFlib + GraphDB = Perfect integration!\")\n", "print(\"   Use SPARQLStore for read access to GraphDB repositories.\")"]}], "metadata": {"kernelspec": {"display_name": "ba (3.13.7)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}