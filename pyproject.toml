[project]
name = "ba"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "click>=8.2.1",
    "json5>=0.12.1",
    "matplotlib>=3.10.6",
    "numpy>=2.3.2",
    "pandas>=2.3.2",
    "prompt-toolkit>=3.0.52",
    "pydantic-ai>=0.8.1",
    "pymupdf>=1.26.4",
    "pypdf2>=3.0.1",
    "pytest>=8.4.1",
    "python-dotenv>=1.1.1",
    "rdflib>=7.1.4",
    "rich>=14.1.0",
    "seaborn>=0.13.2",
    "sparqlwrapper>=2.0.0",
]

[dependency-groups]
dev = [
    "ipykernel>=6.30.1",
]

[tool.uv.workspace]
members = [
    "ConversationalBIM/backend",
]
